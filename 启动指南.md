# 🚀 TodoList 项目启动指南

## 📋 项目概述

这是一个基于 Vue 3 + Spring Boot 的现代化 TodoList 应用，采用前后端分离架构，具有以下特色：

### ✨ 设计特色
- 🎨 **现代化蓝色系UI设计** - 采用渐变背景和毛玻璃效果
- 📱 **完全响应式布局** - 适配桌面、平板、手机等各种设备
- 🎭 **流畅的动画效果** - 页面切换、按钮交互、加载状态等
- 🎯 **直观的用户体验** - 清晰的视觉层次和交互反馈

### 🛠️ 技术栈
- **前端**: Vue 3 + Vite + Pinia + Vue Router + Tailwind CSS
- **后端**: Spring Boot + Spring Data JPA + H2 Database + JWT
- **UI组件**: HeroIcons + 自定义组件库
- **样式**: Tailwind CSS + 自定义CSS变量

## 🚀 快速启动

### 1. 启动后端服务

```bash
# 进入后端目录
cd backend/todolist

# 启动 Spring Boot 应用
mvn spring-boot:run
```

后端服务将在 `http://localhost:8080` 启动

### 2. 启动前端服务

```bash
# 进入前端目录
cd frontend

# 安装依赖（首次运行）
npm install

# 启动开发服务器
npm run dev
```

前端服务将在 `http://localhost:3000` 启动

### 3. 访问应用

打开浏览器访问：`http://localhost:3000`

## 🎨 UI 设计亮点

### 🌈 色彩系统
- **主色调**: 蓝色系 (#3b82f6 - #1e3a8a)
- **辅助色**: 天蓝色系 (#0ea5e9 - #0c4a6e)
- **功能色**: 绿色(成功)、红色(错误)、橙色(警告)
- **中性色**: 灰色系用于文本和背景

### 🎭 视觉效果
- **渐变背景**: 从蓝色到紫色的柔和渐变
- **毛玻璃效果**: backdrop-blur 实现的现代感
- **阴影系统**: 多层次的阴影营造深度感
- **圆角设计**: 统一的圆角半径保持一致性

### 🎪 动画交互
- **页面进入**: fadeIn 和 slideIn 动画
- **按钮悬停**: 轻微上移和阴影变化
- **加载状态**: 旋转动画和脉冲效果
- **模态框**: 缩放和透明度过渡

## 📱 响应式设计

### 桌面端 (≥1024px)
- 三栏统计卡片布局
- 宽松的间距和大尺寸按钮
- 完整的导航栏信息显示

### 平板端 (768px-1023px)
- 自适应的卡片布局
- 中等尺寸的交互元素
- 优化的触摸体验

### 移动端 (≤767px)
- 单列布局
- 全宽按钮和表单
- 简化的导航栏
- 触摸友好的交互区域

## 🧩 组件架构

### 页面组件
- **LoginRegister.vue** - 登录注册页面
- **Todolist.vue** - 主要的任务管理页面

### 通用组件
- **ConfirmDialog.vue** - 确认对话框
- **LoadingSpinner.vue** - 加载动画

### 样式系统
- **style.css** - 全局样式和CSS变量
- **tailwind.config.js** - Tailwind配置
- **自定义类** - btn、input、card等工具类

## 🎯 功能特色

### 🔐 用户认证
- 现代化的登录/注册界面
- JWT令牌认证
- 自动状态保持
- 优雅的错误提示

### 📝 任务管理
- 直观的任务列表
- 内联编辑功能
- 确认删除对话框
- 实时统计信息

### 🎨 用户体验
- 加载状态指示
- 操作反馈动画
- 错误处理提示
- 空状态友好提示

## 🛠️ 开发工具

### 推荐的VS Code插件
- Vue Language Features (Volar)
- Tailwind CSS IntelliSense
- Auto Rename Tag
- Prettier - Code formatter

### 浏览器开发工具
- Vue.js devtools
- Chrome DevTools
- 响应式设计模式

## 📸 截图建议

为了展示项目效果，建议获取以下截图：

1. **登录页面** - 展示现代化的UI设计
2. **注册页面** - 展示表单验证和动画
3. **任务列表** - 展示完整的功能界面
4. **添加任务** - 展示交互过程
5. **编辑任务** - 展示内联编辑功能
6. **删除确认** - 展示确认对话框
7. **响应式布局** - 不同屏幕尺寸的适配
8. **加载状态** - 展示动画效果

## 🔧 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口占用
   netstat -ano | findstr :3000
   netstat -ano | findstr :8080
   ```

2. **依赖安装失败**
   ```bash
   # 清除缓存重新安装
   npm cache clean --force
   rm -rf node_modules package-lock.json
   npm install
   ```

3. **跨域问题**
   - 确保Vite代理配置正确
   - 检查后端CORS设置

4. **样式不生效**
   - 确保Tailwind CSS正确配置
   - 检查CSS文件导入顺序

## 📞 技术支持

如果在启动过程中遇到问题，请检查：
1. Node.js 版本 (推荐 16+)
2. Java 版本 (推荐 17+)
3. Maven 配置
4. 网络连接

## 🎉 享受开发

现在你可以开始体验这个现代化的 TodoList 应用了！

- 🎨 欣赏精美的UI设计
- 🚀 体验流畅的交互动画
- 📱 测试响应式布局
- 🛠️ 探索代码架构

祝你使用愉快！ 🎊
