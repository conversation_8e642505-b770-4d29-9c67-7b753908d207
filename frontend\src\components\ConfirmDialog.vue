<template>
  <!-- 模态框背景 -->
  <Teleport to="body">
    <Transition
      enter-active-class="transition-opacity duration-300"
      enter-from-class="opacity-0"
      enter-to-class="opacity-100"
      leave-active-class="transition-opacity duration-300"
      leave-from-class="opacity-100"
      leave-to-class="opacity-0"
    >
      <div 
        v-if="show" 
        class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        @click="handleBackdropClick"
      >
        <!-- 对话框内容 -->
        <Transition
          enter-active-class="transition-all duration-300"
          enter-from-class="opacity-0 scale-95 translate-y-4"
          enter-to-class="opacity-100 scale-100 translate-y-0"
          leave-active-class="transition-all duration-200"
          leave-from-class="opacity-100 scale-100 translate-y-0"
          leave-to-class="opacity-0 scale-95 translate-y-4"
        >
          <div 
            v-if="show"
            class="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
            @click.stop
          >
            <!-- 头部 -->
            <div class="p-6 pb-4">
              <div class="flex items-center space-x-3">
                <!-- 图标 -->
                <div 
                  class="flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center"
                  :class="iconBgClass"
                >
                  <component :is="iconComponent" class="w-6 h-6" :class="iconClass" />
                </div>
                
                <!-- 标题和描述 -->
                <div class="flex-1">
                  <h3 class="text-lg font-semibold text-gray-900 mb-1">
                    {{ title }}
                  </h3>
                  <p class="text-sm text-gray-600">
                    {{ message }}
                  </p>
                </div>
              </div>
            </div>
            
            <!-- 按钮区域 -->
            <div class="px-6 py-4 bg-gray-50 flex space-x-3 justify-end">
              <button
                @click="handleCancel"
                class="btn btn-ghost px-4 py-2"
                :disabled="loading"
              >
                {{ cancelText }}
              </button>
              <button
                @click="handleConfirm"
                class="btn px-4 py-2"
                :class="confirmButtonClass"
                :disabled="loading"
              >
                <svg v-if="loading" class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {{ confirmText }}
              </button>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import { 
  ExclamationTriangleIcon, 
  InformationCircleIcon, 
  CheckCircleIcon, 
  XCircleIcon 
} from '@heroicons/vue/24/outline'

const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: '确认操作'
  },
  message: {
    type: String,
    default: '您确定要执行此操作吗？'
  },
  type: {
    type: String,
    default: 'warning', // warning, danger, info, success
    validator: (value) => ['warning', 'danger', 'info', 'success'].includes(value)
  },
  confirmText: {
    type: String,
    default: '确认'
  },
  cancelText: {
    type: String,
    default: '取消'
  },
  loading: {
    type: Boolean,
    default: false
  },
  closeOnBackdrop: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['confirm', 'cancel', 'update:show'])

// 根据类型计算图标和样式
const iconComponent = computed(() => {
  switch (props.type) {
    case 'danger':
      return XCircleIcon
    case 'success':
      return CheckCircleIcon
    case 'info':
      return InformationCircleIcon
    default:
      return ExclamationTriangleIcon
  }
})

const iconClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'text-red-600'
    case 'success':
      return 'text-green-600'
    case 'info':
      return 'text-blue-600'
    default:
      return 'text-orange-600'
  }
})

const iconBgClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'bg-red-100'
    case 'success':
      return 'bg-green-100'
    case 'info':
      return 'bg-blue-100'
    default:
      return 'bg-orange-100'
  }
})

const confirmButtonClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'btn-danger'
    case 'success':
      return 'btn-success'
    case 'info':
      return 'btn-primary'
    default:
      return 'bg-orange-600 text-white hover:bg-orange-700 focus:ring-orange-500'
  }
})

// 事件处理
const handleConfirm = () => {
  emit('confirm')
}

const handleCancel = () => {
  emit('cancel')
  emit('update:show', false)
}

const handleBackdropClick = () => {
  if (props.closeOnBackdrop && !props.loading) {
    handleCancel()
  }
}
</script>
