# 软件工程图表下载指南

## 🎯 推荐方法（最简单）

### 方法1：Mermaid Live Editor
1. **访问网站**：https://mermaid.live/
2. **操作步骤**：
   - 打开网站
   - 复制《软件工程图表代码.md》中的任一图表代码
   - 粘贴到左侧编辑器
   - 右侧会自动生成图表
   - 点击右上角"Actions" → "Download PNG"
   - 重复以上步骤下载所有5个图表

### 方法2：直接从对话中保存
1. **查看对话历史**：
   - 在我们的对话中找到已生成的图表
   - 每个图表都应该显示为可交互的图形
2. **保存图片**：
   - 右键点击图表
   - 选择"另存为图片"或"Save image as"
   - 保存为PNG格式

## 🛠️ 备用方法

### 方法3：使用Draw.io
1. **访问网站**：https://app.diagrams.net/
2. **导入Mermaid**：
   - 创建新图表
   - 选择"Advanced" → "Mermaid"
   - 粘贴代码并生成
   - 导出为PNG/JPG

### 方法4：使用VS Code插件
如果你有VS Code：
1. 安装"Mermaid Preview"插件
2. 创建.md文件，粘贴图表代码
3. 预览并截图保存

### 方法5：使用在线截图工具
1. **访问**：https://mermaid.ink/
2. **使用方法**：
   - 将Mermaid代码进行URL编码
   - 访问生成的图片链接
   - 右键保存图片

## 📋 图表清单

需要下载的5个图表：

1. **JWT认证时序图** (图3-1)
   - 文件名建议：`jwt-sequence-diagram.png`
   - 用途：展示用户认证流程

2. **前后端数据交互架构图** (图3-2)
   - 文件名建议：`frontend-backend-architecture.png`
   - 用途：展示系统整体架构

3. **Pinia状态管理流程图** (图3-3)
   - 文件名建议：`pinia-state-flow.png`
   - 用途：展示状态管理机制

4. **部署架构图** (图3-4)
   - 文件名建议：`deployment-architecture.png`
   - 用途：展示部署策略

5. **系统类图** (图2-1)
   - 文件名建议：`system-class-diagram.png`
   - 用途：展示类之间关系

## 🎨 图片质量设置

### 推荐设置
- **格式**：PNG（支持透明背景）
- **分辨率**：1920x1080 或更高
- **DPI**：300（用于打印）或96（用于屏幕）
- **背景**：白色（适合Word文档）

### Word文档插入技巧
1. **插入位置**：在对应的图表说明文字后
2. **大小调整**：宽度设置为15-18cm
3. **对齐方式**：居中对齐
4. **图片标题**：在图片下方添加"图X-X XXX图"
5. **文字环绕**：设置为"上下型"

## 🚀 快速操作步骤

### 5分钟完成所有图表下载：

1. **打开Mermaid Live**：https://mermaid.live/
2. **批量处理**：
   ```
   复制第1个图表代码 → 粘贴 → 下载PNG → 重命名
   复制第2个图表代码 → 粘贴 → 下载PNG → 重命名
   复制第3个图表代码 → 粘贴 → 下载PNG → 重命名
   复制第4个图表代码 → 粘贴 → 下载PNG → 重命名
   复制第5个图表代码 → 粘贴 → 下载PNG → 重命名
   ```
3. **整理文件**：将5个PNG文件放在同一个文件夹中
4. **插入Word**：按顺序插入到报告对应位置

## ❓ 常见问题

### Q1: 图表显示不完整怎么办？
A1: 在Mermaid Live中调整浏览器缩放比例到100%，然后重新下载

### Q2: 图片在Word中显示模糊？
A2: 确保下载的是高分辨率PNG格式，插入Word后不要过度缩放

### Q3: 图表中文显示有问题？
A3: 确保浏览器支持中文字体，或者将中文替换为英文

### Q4: 无法访问在线工具？
A4: 可以使用本地工具如VS Code + Mermaid插件，或者截图保存对话中的图表

## 📞 需要帮助？

如果在下载过程中遇到任何问题，请告诉我具体的错误信息，我会提供更详细的解决方案。
