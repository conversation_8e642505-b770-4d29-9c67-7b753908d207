# TodoList项目软件工程图表代码

## 使用说明
1. 访问 Mermaid 在线编辑器：https://mermaid.live/
2. 复制下面的代码到编辑器中
3. 点击"Download PNG"或"Download SVG"下载图片
4. 将图片插入到Word文档中

---

## 图表1：JWT认证时序图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端Vue
    participant B as 后端SpringBoot
    participant D as 数据库
    
    U->>F: 输入用户名密码
    F->>B: POST /api/v1/auth/login
    B->>D: 验证用户凭据
    D-->>B: 返回用户信息
    B->>B: 生成JWT令牌
    B-->>F: 返回JWT令牌
    F->>F: 存储令牌到localStorage
    F-->>U: 登录成功，跳转主页
    
    Note over F,B: 后续API请求
    U->>F: 操作待办事项
    F->>F: 从localStorage获取令牌
    F->>B: 请求携带Authorization头
    B->>B: JWT拦截器验证令牌
    alt 令牌有效
        B->>D: 执行业务操作
        D-->>B: 返回数据
        B-->>F: 返回API响应
        F-->>U: 更新界面
    else 令牌无效
        B-->>F: 返回401未授权
        F->>F: 清除本地令牌
        F-->>U: 跳转登录页
    end
```

---

## 图表2：前后端数据交互架构图

```mermaid
graph TB
    subgraph "前端层"
        A[Vue 3 应用]
        B[Pinia状态管理]
        C[Vue Router路由]
        D[Axios HTTP客户端]
    end
    
    subgraph "网络层"
        E[HTTP/HTTPS协议]
        F[JSON数据格式]
    end
    
    subgraph "后端层"
        G[Spring Boot应用]
        H[JWT拦截器]
        I[REST Controller]
        J[Service业务层]
        K[JPA Repository]
    end
    
    subgraph "数据层"
        L[H2内存数据库]
        M[用户表]
        N[待办事项表]
    end
    
    A --> B
    A --> C
    A --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M
    L --> N
    
    style A fill:#e1f5fe
    style G fill:#f3e5f5
    style L fill:#e8f5e8
```

---

## 图表3：Pinia状态管理流程图

```mermaid
graph TD
    A[Vue组件] --> B{触发Action}
    B --> C[UserStore.setUser]
    B --> D[UserStore.clearUser]
    B --> E[UserStore.initializeFromStorage]
    
    C --> F[更新State状态]
    D --> G[清空State状态]
    E --> H{检查localStorage}
    
    F --> I[存储到localStorage]
    G --> J[清除localStorage]
    H -->|有数据| K[恢复State状态]
    H -->|无数据| L[保持初始状态]
    
    I --> M[触发响应式更新]
    J --> M
    K --> M
    L --> M
    
    M --> N[更新所有订阅组件]
    N --> O[重新渲染界面]
    
    style A fill:#e1f5fe
    style F fill:#f3e5f5
    style G fill:#ffebee
    style M fill:#e8f5e8
```

---

## 图表4：部署架构图

```mermaid
graph TB
    subgraph "开发环境"
        A[Vue Dev Server<br/>localhost:3000]
        B[Vite代理配置]
        C[Spring Boot<br/>localhost:8080]
    end
    
    subgraph "生产环境"
        D[Spring Boot应用<br/>端口8080]
        E[静态资源目录<br/>/static]
        F[Vue构建文件<br/>index.html, js, css]
    end
    
    subgraph "构建过程"
        G[npm run build]
        H[Vite构建]
        I[输出到后端静态目录]
    end
    
    A --> B
    B --> C
    
    G --> H
    H --> I
    I --> E
    E --> D
    D --> F
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style D fill:#f3e5f5
    style G fill:#fff3e0
```

---

## 图表5：系统类图

```mermaid
classDiagram
    class User {
        -Long id
        -String username
        -String email
        -String password
        +getId() Long
        +getUsername() String
        +getEmail() String
        +setPassword(String)
    }
    
    class Todo {
        -Long id
        -String title
        -Boolean completed
        -LocalDateTime createdAt
        -LocalDateTime updatedAt
        -User user
        +getId() Long
        +getTitle() String
        +isCompleted() Boolean
        +setCompleted(Boolean)
    }
    
    class AuthController {
        -AuthService authService
        -JwtUtils jwtUtils
        +register(RegisterRequest) ApiResponse
        +login(LoginRequest) ApiResponse
    }
    
    class TodoController {
        -TodoService todoService
        +getTodos(HttpServletRequest) ApiResponse
        +createTodo(TodoRequest, HttpServletRequest) ApiResponse
        +updateTodo(Long, TodoRequest, HttpServletRequest) ApiResponse
        +deleteTodo(Long, HttpServletRequest) ApiResponse
    }
    
    class AuthService {
        -UserRepository userRepository
        -PasswordEncoder passwordEncoder
        +register(RegisterRequest) User
        +authenticate(LoginRequest) User
    }
    
    class TodoService {
        -TodoRepository todoRepository
        +findByUserId(Long) List~Todo~
        +createTodo(TodoRequest, Long) Todo
        +updateTodo(Long, TodoRequest, Long) Todo
        +deleteTodo(Long, Long) void
    }
    
    class JwtUtils {
        -SecretKey SECRET_KEY
        -long EXPIRATION_TIME
        +generateToken(Long, String) String
        +validateToken(String) boolean
        +getUserIdFromToken(String) Long
    }
    
    class JwtInterceptor {
        -JwtUtils jwtUtils
        +preHandle(HttpServletRequest, HttpServletResponse, Object) boolean
    }
    
    User ||--o{ Todo : "一对多"
    AuthController --> AuthService : "依赖"
    AuthController --> JwtUtils : "依赖"
    TodoController --> TodoService : "依赖"
    AuthService --> User : "操作"
    TodoService --> Todo : "操作"
    JwtInterceptor --> JwtUtils : "依赖"
```

---

## 图表使用建议

### 1. 图片格式选择
- **PNG格式**：适合Word文档插入，清晰度高
- **SVG格式**：矢量图，可无损缩放，适合印刷

### 2. 图片尺寸建议
- 宽度：800-1200像素
- 高度：根据内容自适应
- DPI：300（用于印刷）或96（用于屏幕显示）

### 3. Word文档插入步骤
1. 在Word中定位到图表位置
2. 插入 → 图片 → 此设备
3. 选择下载的图片文件
4. 调整图片大小和位置
5. 添加图片标题和编号

### 4. 图表标题建议
- 图3-1 JWT认证时序图
- 图3-2 前后端数据交互架构图
- 图3-3 Pinia状态管理流程图
- 图3-4 部署架构图
- 图2-1 系统整体架构类图
