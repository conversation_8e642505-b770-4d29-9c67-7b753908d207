<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useUserStore } from '@/stores/user'
import request from '@/utils/request'  // 改用封装的 request
import { PencilIcon, TrashIcon, CheckIcon, XMarkIcon, ArrowRightOnRectangleIcon } from '@heroicons/vue/24/outline'
import { useRouter } from 'vue-router'
import ConfirmDialog from '@/components/ConfirmDialog.vue'
import LoadingSpinner from '@/components/LoadingSpinner.vue'

const router = useRouter()
const userStore = useUserStore()

// 添加登出函数
const handleLogout = () => {
  userStore.clearUser()
  router.push('/login')
}

const todos = ref([])
const newTodo = ref('')
const isLoading = ref(false)
const showDeleteDialog = ref(false)
const todoToDelete = ref(null)
const isDeleting = ref(false)

// 计算属性
const completedCount = computed(() => todos.value.filter(todo => todo.completed).length)
const pendingCount = computed(() => todos.value.filter(todo => !todo.completed).length)

// 获取待办事项列表
const fetchTodos = async () => {
  try {
    isLoading.value = true
    const { data } = await request.get('/api/v1/todos', {
      params: { userId: userStore.userId }
    })
    
    if (data.code === 200) {
      todos.value = data.data.items
    }
  } catch (error) {
    console.error('获取待办事项失败:', error)
  } finally {
    isLoading.value = false
  }
}

// 添加新待办事项
const addTodo = async () => {
  if (!newTodo.value.trim()) return
  
  try {
    const { data } = await request.post('/api/v1/todos', {
      title: newTodo.value
    }, {
      params: {
        userId: userStore.userId
      }
    })
    
    if (data.code === 200) {
      todos.value.push(data.data)
      newTodo.value = ''
    }
  } catch (error) {
    console.error('添加待办事项失败:', error)
  }
}

const editingTodo = ref(null)
const editContent = ref('')

// 显示删除确认对话框
const showDeleteConfirm = (todo) => {
  todoToDelete.value = todo
  showDeleteDialog.value = true
}

// 确认删除待办事项
const confirmDelete = async () => {
  if (!todoToDelete.value) return

  isDeleting.value = true
  try {
    const { data } = await request.delete(`/api/v1/todos/${todoToDelete.value.id}`, {
      params: { userId: userStore.userId }
    })

    if (data.code === 200) {
      todos.value = todos.value.filter(todo => todo.id !== todoToDelete.value.id)
      showDeleteDialog.value = false
      todoToDelete.value = null
    }
  } catch (error) {
    console.error('删除待办事项失败:', error)
  } finally {
    isDeleting.value = false
  }
}

// 取消删除
const cancelDelete = () => {
  showDeleteDialog.value = false
  todoToDelete.value = null
}

// 开始编辑
const startEdit = async (todo) => {
  editingTodo.value = todo.id
  editContent.value = todo.title
  await nextTick()
  // 聚焦到编辑输入框
  const editInput = document.querySelector('input[ref="editInput"]')
  if (editInput) {
    editInput.focus()
    editInput.select()
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  const now = new Date()
  const diffTime = Math.abs(now - date)
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

  if (diffDays === 1) return '今天'
  if (diffDays === 2) return '昨天'
  if (diffDays <= 7) return `${diffDays}天前`

  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric'
  })
}

// 保存编辑
const saveEdit = async (todo) => {
  try {
    const { data } = await request.put(`/api/v1/todos/${todo.id}`, {
      title: editContent.value,
      completed: todo.completed
    }, {
      params: { userId: userStore.userId }
    })
    
    if (data.code === 200) {
      const index = todos.value.findIndex(t => t.id === todo.id)
      todos.value[index] = data.data
      editingTodo.value = null
    }
  } catch (error) {
    console.error('更新待办事项失败:', error)
  }
}

onMounted(() => {
  fetchTodos()
})
</script>

<template>
  <div class="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50">
    <!-- 导航栏 -->
    <nav class="navbar sticky top-0 z-50">
      <div class="container mx-auto px-4 py-4">
        <div class="flex justify-between items-center">
          <!-- Logo 和标题 -->
          <div class="flex items-center space-x-3">
            <div class="h-10 w-10 bg-gradient-to-r from-blue-600 to-purple-600 rounded-xl flex items-center justify-center shadow-lg">
              <svg class="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div>
              <h1 class="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                TodoList
              </h1>
              <p class="text-xs text-gray-500">让生活更有条理</p>
            </div>
          </div>

          <!-- 用户信息和操作 -->
          <div class="flex items-center space-x-4">
            <div class="hidden sm:flex items-center space-x-2 bg-blue-50 px-3 py-2 rounded-lg">
              <div class="h-8 w-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                <span class="text-white text-sm font-semibold">
                  {{ userStore.username?.charAt(0).toUpperCase() }}
                </span>
              </div>
              <span class="text-gray-700 font-medium">{{ userStore.username }}</span>
            </div>
            <button
              @click="handleLogout"
              class="btn btn-ghost p-2"
              title="登出"
            >
              <ArrowRightOnRectangleIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container mx-auto px-4 py-8 max-w-4xl">
      <!-- 统计卡片 -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl font-bold text-blue-600 mb-2">{{ todos.length }}</div>
            <div class="text-gray-600">总任务</div>
          </div>
        </div>
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl font-bold text-green-600 mb-2">{{ completedCount }}</div>
            <div class="text-gray-600">已完成</div>
          </div>
        </div>
        <div class="card">
          <div class="card-body text-center">
            <div class="text-3xl font-bold text-orange-600 mb-2">{{ pendingCount }}</div>
            <div class="text-gray-600">待完成</div>
          </div>
        </div>
      </div>

      <!-- 添加新待办事项 -->
      <div class="card mb-8">
        <div class="card-body">
          <h2 class="text-lg font-semibold text-gray-800 mb-4 flex items-center">
            <svg class="h-5 w-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            添加新任务
          </h2>
          <form @submit.prevent="addTodo" class="flex flex-col sm:flex-row gap-3">
            <input
              v-model="newTodo"
              type="text"
              placeholder="输入您的新任务..."
              class="input flex-1"
              :disabled="isLoading"
            >
            <button
              type="submit"
              class="btn btn-primary px-6"
              :disabled="isLoading || !newTodo.trim()"
            >
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLoading ? '添加中...' : '添加任务' }}
            </button>
          </form>
        </div>
      </div>

      <!-- 任务列表 -->
      <div class="card">
        <div class="card-header">
          <h2 class="text-lg font-semibold text-gray-800 flex items-center">
            <svg class="h-5 w-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
            </svg>
            我的任务列表
          </h2>
        </div>

        <div class="card-body">
          <!-- 加载状态 -->
          <div v-if="isLoading && todos.length === 0" class="text-center py-12">
            <div class="loading-spinner mx-auto mb-4"></div>
            <p class="text-gray-500">正在加载任务...</p>
          </div>

          <!-- 任务列表 -->
          <div v-else-if="todos.length > 0" class="space-y-3">
            <div
              v-for="(todo, index) in todos"
              :key="todo.id"
              class="group bg-gray-50 hover:bg-white border border-gray-100 hover:border-blue-200 rounded-lg p-4 transition-all duration-200 hover:shadow-md animate-fade-in"
              :style="{ animationDelay: `${index * 0.1}s` }"
            >
              <!-- 编辑模式 -->
              <div v-if="editingTodo === todo.id" class="flex items-center space-x-3">
                <input
                  v-model="editContent"
                  type="text"
                  class="input flex-1"
                  @keyup.enter="saveEdit(todo)"
                  @keyup.escape="editingTodo = null"
                  ref="editInput"
                >
                <button
                  @click="saveEdit(todo)"
                  class="btn btn-success p-2"
                  title="保存"
                >
                  <CheckIcon class="h-4 w-4" />
                </button>
                <button
                  @click="editingTodo = null"
                  class="btn btn-ghost p-2"
                  title="取消"
                >
                  <XMarkIcon class="h-4 w-4" />
                </button>
              </div>

              <!-- 显示模式 -->
              <div v-else class="flex items-center justify-between">
                <div class="flex items-center space-x-3 flex-1">
                  <div class="flex-shrink-0">
                    <div class="h-2 w-2 bg-blue-400 rounded-full"></div>
                  </div>
                  <span class="text-gray-800 font-medium">{{ todo.title }}</span>
                  <span class="text-xs text-gray-400 bg-gray-100 px-2 py-1 rounded-full">
                    {{ formatDate(todo.createdAt) }}
                  </span>
                </div>

                <div class="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <button
                    @click="startEdit(todo)"
                    class="btn btn-ghost p-2 text-blue-600 hover:text-blue-700"
                    title="编辑"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </button>
                  <button
                    @click="showDeleteConfirm(todo)"
                    class="btn btn-ghost p-2 text-red-600 hover:text-red-700"
                    title="删除"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else class="text-center py-12">
            <div class="mx-auto h-24 w-24 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-6">
              <svg class="h-12 w-12 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <h3 class="text-lg font-medium text-gray-800 mb-2">还没有任务</h3>
            <p class="text-gray-500 mb-6">开始添加您的第一个任务，让生活更有条理</p>
            <button @click="$refs.newTodoInput?.focus()" class="btn btn-primary">
              <svg class="h-4 w-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
              </svg>
              添加第一个任务
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 删除确认对话框 -->
    <ConfirmDialog
      v-model:show="showDeleteDialog"
      type="danger"
      title="删除任务"
      :message="`确定要删除任务「${todoToDelete?.title}」吗？此操作无法撤销。`"
      confirm-text="删除"
      cancel-text="取消"
      :loading="isDeleting"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>