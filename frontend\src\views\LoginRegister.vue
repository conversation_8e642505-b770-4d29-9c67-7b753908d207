<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import axios from 'axios'
import { useUserStore } from '@/stores/user'

const router = useRouter()
const userStore = useUserStore()
const isLogin = ref(true) // true为登录模式，false为注册模式
const email = ref('')
const password = ref('')
const confirmPassword = ref('')
const errorMessage = ref('')
const username = ref('')
const isLoading = ref(false)

// 登录处理
const handleLogin = async () => {
  if (!email.value || !password.value) {
    errorMessage.value = '请填写邮箱和密码'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const { data } = await axios.post('/api/v1/auth/login', {
      email: email.value,
      password: password.value
    })
    
    // 登录处理中的保存逻辑修改为：
    if (data.code != 200) {
      errorMessage.value = data.message || '邮箱或密码错误'
      return
    }

    // 保存到 pinia store
    userStore.setUser({
      userId: data.data.userId,
      username: data.data.username,
      token: data.data.token
    })

    // 设置 axios 默认请求头
    axios.defaults.headers.common['Authorization'] = `Bearer ${data.data.token}`

    errorMessage.value = '登录成功'
    setTimeout(() => {
      router.push('/todolist')
    }, 1000)

  } catch (error) {
    console.error('登录请求失败:', error)
    errorMessage.value = '登录失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 注册处理
const handleRegister = async () => {
  if (!email.value || !password.value || !confirmPassword.value || !username.value) {
    errorMessage.value = '请填写所有字段'
    return
  }

  if (password.value !== confirmPassword.value) {
    errorMessage.value = '两次输入的密码不一致'
    return
  }

  isLoading.value = true
  errorMessage.value = ''

  try {
    const { data } = await axios.post('/api/v1/auth/register', {
      username: username.value,
      password: password.value,
      email: email.value
    })

    if (data.code !== 200) {
      if (data.message?.includes('邮箱已存在')) {
        errorMessage.value = '该邮箱已被注册，请使用其他邮箱'
      } else {
        errorMessage.value = data.message || '注册失败，请重试'
      }
      return
    }
    // 注册成功，切换到登录模式
    errorMessage.value = '注册成功，请登录'
    isLogin.value = true
    email.value = ''
    password.value = ''
    confirmPassword.value = ''
  } catch (error) {
    console.error('注册请求失败:', error)
    errorMessage.value = '注册失败，请稍后重试'
  } finally {
    isLoading.value = false
  }
}

// 切换登录/注册模式
const toggleMode = () => {
  isLogin.value = !isLogin.value
  email.value = ''
  password.value = ''
  confirmPassword.value = ''
  errorMessage.value = ''
}
</script>

<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50"></div>
    <div class="absolute top-0 left-0 w-full h-full">
      <div class="absolute top-10 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse"></div>
      <div class="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-2000"></div>
      <div class="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-pulse animation-delay-4000"></div>
    </div>

    <!-- 主要内容 -->
    <div class="relative z-10 max-w-md w-full">
      <!-- Logo 和标题区域 -->
      <div class="text-center mb-8 animate-fade-in">
        <div class="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl flex items-center justify-center mb-4 shadow-lg">
          <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
          </svg>
        </div>
        <h2 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
          {{ isLogin ? '欢迎回来' : '创建账户' }}
        </h2>
        <p class="mt-2 text-gray-600">
          {{ isLogin ? '登录您的TodoList账户' : '开始您的高效之旅' }}
        </p>
      </div>

      <!-- 表单卡片 -->
      <div class="card animate-slide-in">
        <div class="card-body">
          <!-- 错误/成功信息 -->
          <div v-if="errorMessage"
               :class="[
                 'p-4 rounded-lg text-sm font-medium mb-6 transition-all duration-300',
                 errorMessage.includes('成功')
                   ? 'bg-green-50 text-green-700 border border-green-200'
                   : 'bg-red-50 text-red-700 border border-red-200'
               ]">
            <div class="flex items-center">
              <svg v-if="errorMessage.includes('成功')" class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <svg v-else class="h-5 w-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
              </svg>
              {{ errorMessage }}
            </div>
          </div>

          <form @submit.prevent="isLogin ? handleLogin() : handleRegister()" class="space-y-6">
            <!-- 邮箱输入 -->
            <div>
              <label for="email" class="block text-sm font-medium text-gray-700 mb-2">邮箱地址</label>
              <input
                id="email"
                v-model="email"
                type="email"
                required
                class="input"
                placeholder="请输入您的邮箱"
              >
            </div>

            <!-- 用户名输入（仅注册时显示） -->
            <div v-if="!isLogin" class="animate-fade-in">
              <label for="username" class="block text-sm font-medium text-gray-700 mb-2">用户名</label>
              <input
                id="username"
                v-model="username"
                type="text"
                required
                class="input"
                placeholder="请输入用户名"
              >
            </div>

            <!-- 密码输入 -->
            <div>
              <label for="password" class="block text-sm font-medium text-gray-700 mb-2">密码</label>
              <input
                id="password"
                v-model="password"
                type="password"
                required
                class="input"
                placeholder="请输入密码"
              >
            </div>

            <!-- 确认密码输入（仅注册时显示） -->
            <div v-if="!isLogin" class="animate-fade-in">
              <label for="confirm-password" class="block text-sm font-medium text-gray-700 mb-2">确认密码</label>
              <input
                id="confirm-password"
                v-model="confirmPassword"
                type="password"
                required
                class="input"
                placeholder="请再次输入密码"
              >
            </div>

            <!-- 提交按钮 -->
            <button type="submit" class="btn btn-primary w-full py-3 text-base font-semibold">
              <svg v-if="isLoading" class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              {{ isLogin ? '登录' : '注册' }}
            </button>
          </form>

          <!-- 切换模式 -->
          <div class="mt-6 text-center">
            <button @click="toggleMode" class="btn btn-ghost text-sm">
              {{ isLogin ? '没有账户？立即注册' : '已有账户？立即登录' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 底部信息 -->
      <div class="mt-8 text-center text-sm text-gray-500 animate-fade-in">
        <p>© 2024 TodoList. 让生活更有条理</p>
      </div>
    </div>
  </div>
</template>