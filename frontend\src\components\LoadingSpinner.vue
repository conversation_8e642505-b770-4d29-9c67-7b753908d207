<template>
  <div class="flex items-center justify-center" :class="containerClass">
    <div class="relative">
      <!-- 主要加载动画 -->
      <div 
        class="animate-spin rounded-full border-4 border-blue-200"
        :class="[
          size === 'sm' ? 'h-6 w-6 border-2' : 
          size === 'lg' ? 'h-12 w-12 border-4' : 
          'h-8 w-8 border-3'
        ]"
        :style="{ borderTopColor: color }"
      ></div>
      
      <!-- 内部点动画 -->
      <div 
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 rounded-full animate-pulse"
        :class="[
          size === 'sm' ? 'h-1 w-1' : 
          size === 'lg' ? 'h-2 w-2' : 
          'h-1.5 w-1.5'
        ]"
        :style="{ backgroundColor: color }"
      ></div>
    </div>
    
    <!-- 加载文本 -->
    <span v-if="text" class="ml-3 text-gray-600 font-medium" :class="textClass">
      {{ text }}
    </span>
  </div>
</template>

<script setup>
defineProps({
  size: {
    type: String,
    default: 'md', // sm, md, lg
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  color: {
    type: String,
    default: '#3b82f6' // blue-500
  },
  text: {
    type: String,
    default: ''
  },
  containerClass: {
    type: String,
    default: ''
  },
  textClass: {
    type: String,
    default: ''
  }
})
</script>

<style scoped>
.border-3 {
  border-width: 3px;
}
</style>
