# TodoList项目功能截图说明

## 截图获取指南

为了完善实验报告中的功能展示部分，需要获取以下关键功能的截图：

### 4.1 用户认证功能截图

#### 4.1.1 用户注册界面截图
**截图位置**: 访问 `http://localhost:3000` 点击"注册"标签
**截图内容应包含**:
- 用户名输入框
- 邮箱输入框  
- 密码输入框
- 确认密码输入框
- 注册按钮
- 表单验证提示信息

#### 4.1.2 用户登录界面截图
**截图位置**: 访问 `http://localhost:3000` 默认登录页面
**截图内容应包含**:
- 邮箱输入框
- 密码输入框
- 登录按钮
- 注册链接
- 错误提示信息（如果有）

#### 4.1.3 登录状态展示截图
**截图位置**: 成功登录后的主页面
**截图内容应包含**:
- 顶部导航栏显示用户名
- 退出登录按钮
- 主要功能区域

### 4.2 待办事项管理功能截图

#### 4.2.1 待办事项列表截图
**截图位置**: 登录后的主页面 `http://localhost:3000/todos`
**截图内容应包含**:
- 待办事项列表
- 每个事项的标题、时间、状态
- 添加新事项的输入框
- 编辑、删除按钮

#### 4.2.2 添加待办事项截图
**截图位置**: 在输入框中输入内容的状态
**截图内容应包含**:
- 输入框中的文本内容
- 添加按钮的激活状态
- 输入提示信息

#### 4.2.3 编辑待办事项截图
**截图位置**: 点击编辑按钮后的状态
**截图内容应包含**:
- 编辑模式下的输入框
- 保存和取消按钮
- 编辑状态的视觉反馈

#### 4.2.4 删除确认对话框截图
**截图位置**: 点击删除按钮后弹出的确认框
**截图内容应包含**:
- 删除确认对话框
- 确认和取消按钮
- 警告提示信息

### 4.3 用户界面设计截图

#### 4.3.1 响应式布局截图
**需要获取多个截图**:
- 桌面端（1920x1080）界面截图
- 平板端（768x1024）界面截图  
- 移动端（375x667）界面截图
**截图内容应展示**:
- 不同屏幕尺寸下的布局适配
- 导航栏的响应式变化
- 内容区域的自适应调整

#### 4.3.2 现代化UI设计截图
**截图位置**: 主要功能页面的整体视图
**截图内容应包含**:
- 整体的色彩搭配
- 按钮和表单的设计风格
- 图标和字体的使用
- 间距和布局的美观性

#### 4.3.3 交互反馈截图
**需要获取多个状态截图**:
- 加载状态截图（显示loading动画）
- 成功提示截图（操作成功的消息）
- 错误提示截图（网络错误或操作失败）
- 表单验证截图（输入格式错误提示）

## 截图获取步骤

### 1. 启动项目
```bash
# 启动后端
cd backend/todolist
mvn spring-boot:run

# 启动前端
cd frontend
npm run dev
```

### 2. 准备测试数据
- 注册一个测试用户账号
- 添加几个示例待办事项
- 准备不同状态的数据（已完成、未完成）

### 3. 截图工具推荐
- Windows: Snipping Tool 或 Snagit
- macOS: Command+Shift+4 或 CleanShot X
- 浏览器: 开发者工具的设备模拟功能

### 4. 截图规范
- 分辨率: 至少1920x1080
- 格式: PNG（保证清晰度）
- 命名: 按功能模块命名，如 `login-page.png`
- 尺寸: 适合Word文档插入的大小

## 截图后处理

### 1. 图片优化
- 裁剪掉不必要的浏览器边框
- 调整图片大小适合文档排版
- 确保文字清晰可读

### 2. 添加标注
- 可以添加箭头指向关键功能
- 用红框标出重要区域
- 添加必要的文字说明

### 3. 插入文档
- 在Word文档中对应位置插入截图
- 添加图片标题和编号
- 确保图片与文字说明对应

## 注意事项

1. **隐私保护**: 截图中不要包含真实的个人信息
2. **功能完整性**: 确保截图展示的功能是完整可用的
3. **视觉效果**: 选择最佳的界面状态进行截图
4. **一致性**: 保持所有截图的风格和质量一致
5. **文档格式**: 确保截图在Word文档中显示正常

通过以上截图的获取和整理，可以大大提升实验报告的专业性和完整性，为项目展示提供有力的视觉支撑。
