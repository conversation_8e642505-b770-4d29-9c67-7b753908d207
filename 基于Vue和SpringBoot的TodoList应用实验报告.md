基于Vue和Spring Boot的前后端分离TodoList应用设计与实现

实验报告

学生姓名：[姓名]
学号：[学号]
班级：[班级]
指导教师：[教师姓名]
完成时间：2024年12月

1. 项目概述

1.1 项目背景
本项目旨在通过开发一个完整的TodoList应用，掌握前后端分离架构的设计思想和实现方法，熟悉Vue框架和Spring Boot后端开发的核心技术。

1.2 项目目标
• 掌握前后端分离架构设计
• 熟悉Vue组件化开发和状态管理
• 掌握Spring Boot后端开发核心技术
• 理解RESTful API设计和JWT认证机制
• 学习前后端集成部署和跨域问题解决

1.3 主要功能
• 用户注册与登录认证
• JWT令牌管理
• 待办事项的增删改查
• 用户状态管理
• 响应式用户界面

2. 系统设计

2.1 架构设计
采用前后端分离架构：
• 前端: Vue 3 + Vite + Pinia + Vue Router + Tailwind CSS
• 后端: Spring Boot + Spring Data JPA + H2 Database + JWT
• 通信: RESTful API + JSON数据格式

2.2 数据库设计

用户表 (users)
字段名      类型           约束                    说明
id         BIGINT         PRIMARY KEY, AUTO_INCREMENT    用户ID
username   VARCHAR(50)    NOT NULL                用户名
email      VARCHAR(100)   NOT NULL, UNIQUE        邮箱
password   VARCHAR(255)   NOT NULL                加密密码

待办事项表 (todos)
字段名        类型           约束                    说明
id           BIGINT         PRIMARY KEY, AUTO_INCREMENT    待办事项ID
title        VARCHAR(255)   NOT NULL                标题
completed    BOOLEAN        DEFAULT FALSE           完成状态
created_at   TIMESTAMP      NOT NULL                创建时间
updated_at   TIMESTAMP                              更新时间
user_id      BIGINT         FOREIGN KEY             用户ID

2.3 API接口设计

认证接口
• POST /api/v1/auth/register - 用户注册
• POST /api/v1/auth/login - 用户登录

待办事项接口
• GET /api/v1/todos - 获取待办事项列表
• POST /api/v1/todos - 创建待办事项
• PUT /api/v1/todos/{id} - 更新待办事项
• DELETE /api/v1/todos/{id} - 删除待办事项

3. 关键技术实现

3.1 用户认证与JWT实现

后端JWT工具类实现：
使用JJWT库生成和验证JWT令牌，设置24小时有效期，包含用户ID和用户名信息。

JWT拦截器实现：
拦截所有需要认证的请求，从请求头中提取Bearer Token，验证有效性后将用户信息存储到请求属性中。

3.2 前后端数据交互

前端请求封装：
创建Axios实例，使用请求拦截器自动添加JWT令牌到Authorization头部，统一处理认证逻辑。

统一响应格式：
定义ApiResponse类，包含code、message、data字段，提供success和error静态方法，确保前后端数据交互的一致性。

3.3 状态管理实现

Pinia用户状态管理：
定义用户状态store，包含userId、username、token字段，提供setUser和clearUser方法，支持localStorage持久化。

3.4 前后端集成部署方案

前端代理配置：
使用Vite开发服务器的代理功能，将/api请求转发到后端服务器，解决开发环境的跨域问题。

4. 功能展示

4.1 用户认证功能
✓ 用户注册：支持邮箱、用户名、密码注册
✓ 用户登录：邮箱密码登录，返回JWT令牌
✓ 登录状态保持：localStorage持久化
✓ 路由守卫：未登录自动跳转登录页

4.2 待办事项管理
✓ 查看待办列表：显示用户的所有待办事项
✓ 添加待办事项：输入标题创建新的待办事项
✓ 编辑待办事项：点击编辑按钮修改标题
✓ 删除待办事项：点击删除按钮移除待办事项

4.3 用户界面
✓ 响应式设计：适配不同屏幕尺寸
✓ 现代化UI：使用Tailwind CSS美化界面
✓ 交互友好：加载状态、错误提示等

5. 问题与解决方案

5.1 跨域问题
问题：前后端分离开发时出现跨域请求被阻止
解决方案：
• 开发环境：使用Vite代理配置转发API请求
• 生产环境：可配置Spring Boot CORS支持

5.2 JWT令牌管理
问题：每次请求都需要手动添加Authorization头
解决方案：
• 创建Axios请求拦截器自动添加JWT令牌
• 统一管理令牌的存储和清除

5.3 状态管理
问题：页面刷新后用户登录状态丢失
解决方案：
• 使用localStorage持久化用户信息
• 路由守卫中检查并恢复登录状态

5.4 密码安全
问题：用户密码需要安全存储
解决方案：
• 使用Spring Security的BCryptPasswordEncoder加密密码
• 前端不存储敏感信息，仅保存JWT令牌

6. 心得体会

6.1 技术收获
1. 前后端分离架构理解：深入理解了前后端分离的优势和实现方式
2. Vue生态系统：熟练掌握了Vue 3、Pinia、Vue Router等核心技术
3. Spring Boot开发：学会了RESTful API设计和JWT认证实现
4. 数据库设计：掌握了JPA实体关系映射和数据库操作

6.2 开发体验
1. 组件化开发：Vue的组件化开发提高了代码复用性和维护性
2. 状态管理：Pinia提供了简洁的状态管理方案
3. 开发效率：Vite的热重载和快速构建大大提升了开发效率
4. API设计：RESTful API设计规范使前后端协作更加顺畅

6.3 项目管理
1. 版本控制：合理使用Git进行代码版本管理
2. 模块化设计：良好的项目结构便于后期维护和扩展
3. 错误处理：统一的错误处理机制提升了用户体验

7. 项目总结

7.1 完成情况
• 基础功能：已完成用户认证、JWT管理、Todo CRUD等核心功能
• 技术栈：成功集成Vue + Spring Boot技术栈
• 前后端集成：实现了完整的前后端数据交互

7.2 待改进功能
• Todo项的优先级和截止日期功能
• 完善的筛选和排序功能
• 用户头像上传功能
• 多用户Todo共享功能

7.3 学习成果
通过本项目的开发，全面掌握了现代Web应用的开发流程，从需求分析、系统设计到具体实现，每个环节都得到了实践锻炼。特别是对前后端分离架构有了深入的理解，为今后的Web开发奠定了坚实的基础。

8. 参考资料

1. Vue.js官方文档: https://vuejs.org/
2. Spring Boot官方文档: https://spring.io/projects/spring-boot
3. JWT官方网站: https://jwt.io/
4. Tailwind CSS文档: https://tailwindcss.com/
5. Axios文档: https://axios-http.com/
6. Pinia状态管理: https://pinia.vuejs.org/

项目完成时间：2024年12月
开发环境：Windows 11, Node.js 18+, Java 17, Maven 3.8+
