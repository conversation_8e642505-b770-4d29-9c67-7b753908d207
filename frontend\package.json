{"name": "todolist", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@heroicons/vue": "^2.2.0", "axios": "^1.8.4", "pinia": "^3.0.1", "vue": "^3.5.13", "vue-router": "^4.5.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "autoprefixer": "^10.4.17", "postcss": "^8.4.33", "tailwindcss": "^3.4.1", "vite": "^6.2.1", "vite-plugin-vue-devtools": "^7.7.2"}}